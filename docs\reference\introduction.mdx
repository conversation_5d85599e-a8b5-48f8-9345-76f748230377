---
title: 'Stagehand SDK Reference'
description: 'View each method in the Stagehand SDK and learn how to use them.'
---



<CardGroup cols={2}>
  <Card
    title="Configuration"
    icon="gear"
    href="/reference/initialization_config"
  >
    Configure Stagehand the way you want.
  </Card>
  <Card
    title="Agent"
    icon="robot"
    href="/reference/agent"
  >
    Web AI agents for any task
  </Card>
  <Card
    title="Act"
    icon="arrow-pointer"
    href="/reference/act"
  >
    Perform actions on the current page.
  </Card>
  <Card
    title="Extract"
    icon="brain-circuit"
    href="/reference/extract"
  >
    Extract structured data from the page.
  </Card>
  <Card
    title="Observe"
    icon="lightbulb"
    href="/reference/observe"
  >
    Get candidate DOM elements for actions.
  </Card>
  <Card
    title="Playwright Interoperability"
    icon="right-to-bracket"
    href="/reference/playwright_interop"
  >
    Combine Stagehand with Playwright.
  </Card>

</CardGroup>
