{"$schema": "https://mintlify.com/docs.json", "theme": "willow", "name": "🤘 Stagehand", "colors": {"primary": "#B88100", "light": "#FFC83C", "dark": "#FFC83C"}, "favicon": "/images/favicon.svg", "navigation": {"groups": [{"group": "Get Started", "pages": ["get_started/introduction", "get_started/quickstart", "get_started/integrate_stagehand"]}, {"group": "Concepts", "pages": ["concepts/act", "concepts/agent"]}, {"group": "Playbooks", "pages": ["examples/best_practices", "examples/caching", "examples/running_evals", "examples/nextjs", "examples/custom_llms", "examples/customize_browser", "examples/computer_use", "examples/contributing"]}, {"group": "Reference", "pages": ["reference/initialization_config", "reference/agent", "reference/act", "reference/extract", "reference/observe", "reference/playwright_interop", "integrations/guides"]}, {"group": "Integrations", "pages": [{"group": "MCP Server", "pages": ["integrations/mcp/introduction", "integrations/mcp/setup", "integrations/mcp/tools", "integrations/mcp/configuration"]}, "integrations/langchain", "integrations/crew-ai"]}], "global": {"anchors": [{"anchor": "Changelog", "href": "https://github.com/browserbase/stagehand/releases", "icon": "scroll"}, {"anchor": "Reference", "href": "https://docs.stagehand.dev/reference/introduction", "icon": "code"}]}}, "logo": {"light": "/logo/light_logo.png", "dark": "/logo/dark_logo.png", "href": "https://stagehand.dev"}, "navbar": {"links": [{"label": "Support", "href": "mailto:<EMAIL>"}]}, "footer": {"socials": {"x": "https://x.com/stagehanddev", "github": "https://github.com/browserbase/stagehand", "linkedin": "https://linkedin.com/company/browserbasehq", "slack": "https://stagehand.dev/slack"}}, "integrations": {"koala": {"publicApiKey": "pk_6e508a479e95d94298f034e98ec54590ccf4"}}}