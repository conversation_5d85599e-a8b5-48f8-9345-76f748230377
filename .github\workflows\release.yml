name: Release

on:
  push:
    branches:
      - main

permissions:
  contents: write
  pull-requests: write

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v3
        with:
          node-version: 20.x
          registry-url: "https://registry.npmjs.org"

      - name: Install dependencies
        run: |
          rm -rf node_modules
          npm install -g pnpm
          pnpm install --no-frozen-lockfile

      - name: Build
        run: pnpm run build

      - name: Create Release Pull Request or Publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          publish: pnpm run release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Publish Canary
        if: github.ref == 'refs/heads/main'
        run: |
          pnpm config set //registry.npmjs.org/:_authToken=${NODE_AUTH_TOKEN}
          git checkout main
          pnpm run release-canary
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
