---
title: "Browserbase MCP Server Configuration"
sidebarTitle: "Configuration"
description: "Configure your browser automation with command-line flags, environment variables, and advanced options"
---

## Configuration Overview

The Browserbase MCP server supports extensive configuration options through command-line flags and environment variables. Configure browser behavior, proxy settings, stealth modes, model selection, and more to customize your browser automation workflows.

<Note>
Command-line flags are only available when running the server locally (`npx @browserbasehq/mcp-server-browserbase` with flags or local development setup).
</Note>

## Environment Variables

Configure the essential Browserbase credentials and optional debugging settings:

<CardGroup cols={2}>
<Card title="BROWSERBASE_API_KEY" icon="key">
Your Browserbase API key for authentication
</Card>

<Card title="BROWSERBASE_PROJECT_ID" icon="key">
Your Browserbase project ID
</Card>

</CardGroup>

## Command-Line Flags

### Available Flags

| Flag | Description |
|------|-------------|
| `--proxies` | Enable Browserbase proxies for the session |
| `--advancedStealth` | Enable Browserbase Advanced Stealth (Scale Plan only) |
| `--contextId <contextId>` | Specify a Browserbase Context ID to use |
| `--persist [boolean]` | Whether to persist the Browserbase context (default: true) |
| `--port <port>` | Port to listen on for HTTP/SHTTP transport |
| `--host <host>` | Host to bind server to (default: localhost, use 0.0.0.0 for all interfaces) |
| `--cookies [json]` | JSON array of cookies to inject into the browser |
| `--browserWidth <width>` | Browser viewport width (default: 1024) |
| `--browserHeight <height>` | Browser viewport height (default: 768) |
| `--modelName <model>` | The model to use for Stagehand (default: gemini-2.0-flash) |
| `--modelApiKey <key>` | API key for the custom model provider (required when using custom models) |

## Configuration Examples

### Basic Configuration

<Tabs>
<Tab title="Remote URL (SHTTP)">


<CodeGroup>
```json Direct SHTTP
{
  "mcpServers": {
    "browserbase": {
      "url": "your-smithery-url.com"
    }
  }
}
```
</CodeGroup>

When using our remote hosted server, we provide the LLM costs for Gemini, the [best performing model](https://www.stagehand.dev/evals) in [Stagehand](https://www.stagehand.dev).
</Tab>

<Tab title="NPM Package">

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": ["@browserbasehq/mcp-server-browserbase"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```
</Tab>

<Tab title="Local STDIO">
```json
{
  "mcpServers": {
    "browserbase": {
      "command": "node",
      "args": ["/path/to/mcp-server-browserbase/cli.js"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```
</Tab>

<Tab title="Local SHTTP">
```bash
# Start server
node cli.js --port 8931
```

```json
{
  "mcpServers": {
    "browserbase": {
      "url": "http://localhost:8931/mcp",
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```
</Tab>
</Tabs>

### Advanced Features

<Tabs>
<Tab title="Proxies">
Enable Browserbase proxies for IP rotation and geo-location testing.

<Panel>
[Learn more about Browserbase Proxies](https://docs.browserbase.com/features/proxies)
</Panel>

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": ["@browserbasehq/mcp-server-browserbase", "--proxies"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```
</Tab>

<Tab title="Advanced Stealth">
Enable advanced anti-detection features for enhanced stealth browsing.

<Panel>
[Learn more about Advanced Stealth](https://docs.browserbase.com/features/stealth-mode#advanced-stealth-mode)

**Note:** Advanced Stealth is only available for Scale Plan users.
</Panel>

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": ["@browserbasehq/mcp-server-browserbase", "--advancedStealth"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```
</Tab>

<Tab title="Contexts">
Use persistent browser contexts to maintain authentication and state across sessions.

<Panel>
[Learn more about Browserbase Contexts](https://docs.browserbase.com/features/contexts)
</Panel>

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": ["@browserbasehq/mcp-server-browserbase", "--contextId", "your_context_id"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id"
      }
    }
  }
}
```
</Tab>
</Tabs>

### Browser Customization

<Tabs>
<Tab title="Viewport Sizing">
Customize browser window dimensions. Default is 1024x768. Recommended aspect ratios: 16:9.

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": [
        "@browserbasehq/mcp-server-browserbase",
        "--browserWidth", "1920",
        "--browserHeight", "1080"
      ],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```

**Common Resolutions:**
- Desktop: 1920x1080, 1280x720, 1024x768
- Mobile: 375x667 (iPhone), 360x640 (Android)
- Tablet: 768x1024 (iPad)
</Tab>

<Tab title="Cookie Injection">
Inject session cookies for authentication. Useful when persistent contexts don't handle session cookies.

<Panel>
Cookies must be in [Playwright Cookie format](https://playwright.dev/docs/api/class-browsercontext#browser-context-cookies).
</Panel>

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": [
        "@browserbasehq/mcp-server-browserbase",
        "--cookies",
        "[{\"name\": \"session\", \"value\": \"abc123\", \"domain\": \".example.com\", \"path\": \"/\", \"httpOnly\": true, \"secure\": true}]"
      ],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```
</Tab>
</Tabs>

## Model Configuration

Configure AI models for enhanced browser automation. Stagehand defaults to Google's Gemini 2.0 Flash but supports multiple providers.

<Warning>
When using any custom model (non-default), you must provide your own API key for that model provider using the `--modelApiKey` flag.
</Warning>

<Tabs>
<Tab title="Available Models">
**Google Gemini** (Default)
- `google/gemini-2.0-flash` (default)
- `google/gemini-1.5-pro`
- `google/gemini-1.5-flash`

**OpenAI**
- `openai/gpt-4o`
- `openai/gpt-4o-mini`
- `openai/o1-mini`
- `openai/o1-preview`
- `openai/o3-mini`

**Anthropic Claude**
- `anthropic/claude-3-5-sonnet-latest`
- `anthropic/claude-3-7-sonnet-latest`

[View full list of supported models](https://docs.stagehand.dev/examples/custom_llms#supported-llms)
</Tab>

<Tab title="Configuration Examples">
<CodeGroup>
```json OpenAI GPT-4o
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": [
        "@browserbasehq/mcp-server-browserbase",
        "--modelName", "openai/gpt-4o",
        "--modelApiKey", "your_openai_api_key"
      ],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id"
      }
    }
  }
}
```

```json Claude Sonnet
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": [
        "@browserbasehq/mcp-server-browserbase",
        "--modelName", "anthropic/claude-3-5-sonnet-latest",
        "--modelApiKey", "your_anthropic_api_key"
      ],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id"
      }
    }
  }
}
```
</CodeGroup>
</Tab>
</Tabs>

## Development Configuration

<Tabs>
<Tab title="Debug Mode">
Enable detailed logging for troubleshooting and development.

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": ["@browserbasehq/mcp-server-browserbase"],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key",
        "DEBUG": "true"
      }
    }
  }
}
```
</Tab>

<Tab title="Custom Host/Port">
Configure custom host and port for SHTTP transport.

```json
{
  "mcpServers": {
    "browserbase": {
      "command": "npx",
      "args": [
        "@browserbasehq/mcp-server-browserbase",
        "--host", "0.0.0.0",
        "--port", "8080"
      ],
      "env": {
        "BROWSERBASE_API_KEY": "your_api_key",
        "BROWSERBASE_PROJECT_ID": "your_project_id",
        "GEMINI_API_KEY": "your_gemini_api_key"
      }
    }
  }
}
```
</Tab>
</Tabs>

## Best Practices

<Accordion title="Performance - How can I optimize browser automation performance?">
- Use appropriate viewport sizes for your use case
- Enable proxies only when needed for geo-location
- Choose efficient models (Gemini Flash for speed, GPT-4o for accuracy)
- Reuse contexts for authentication persistence
</Accordion>

<Accordion title="Security - What security measures should I implement?">
- Store API keys securely in environment variables
- Use Advanced Stealth for sensitive operations
- Implement proper session management
- Rotate cookies and contexts regularly
</Accordion>

<Accordion title="Development - What are the recommended development practices?">
- Enable debug mode during development
- Use context persistence for faster iteration
- Test with different viewport sizes
- Monitor session usage and quotas
</Accordion>

<Accordion title="Production - How should I configure for production environments?">
- Use NPM installation for reliability
- Configure appropriate timeouts
- Implement error handling and retries
- Monitor performance and resource usage
</Accordion>

## Further Reading

<CardGroup cols={3}>
<Card title="Browserbase Documentation" icon="globe" href="https://docs.browserbase.com">
Complete platform documentation
</Card>

<Card title="Stagehand Docs" icon="robot" href="https://docs.stagehand.dev/">
AI-powered browser automation
</Card>

<Card title="Support" icon="headset" href="mailto:<EMAIL>">
Get help from our team
</Card>
</CardGroup>