---
title: 'Quickstart'
icon: 'rocket'
---

You can get started with Stagehand in just 1 minute! Choose your preferred language below.

<Tip>
For TypeScript/Node.js: We highly recommend using the Node.js runtime environment to run Stagehand scripts, as opposed to newer alternatives like Deno or Bun. 

**Bun does not support Stagehand** since it doesn't support [<PERSON><PERSON>](https://github.com/search?q=repo:oven-sh/bun+playwright&type=issues).

For Python: We require Python 3.9+ and recommend using [uv](https://docs.astral.sh/uv/) to manage your virtual environment.
</Tip>


<Tabs>
<Tab title="TypeScript">
	Before you begin, you'll need to install Node.js and NPM. We highly recommend using [nvm](https://github.com/nvm-sh/nvm) to manage your Node.js versions, and running on Node version 20+.

<Steps>

<Step title="Create a new project">

You can use [npx](https://docs.npmjs.com/cli/v8/commands/npx) to create a new project. You should have npx included with `npm`, the default package manager for Node.js. 

<Tabs>
	<Tab title="npm">
	```bash
	npx create-browser-app
	```
	</Tab>

	<Tab title="pnpm">
	```bash
	pnpm create browser-app
	```
	</Tab>

	<Tab title="yarn">
	```bash
	yarn create browser-app
	```
	</Tab>
</Tabs>

To use our [Contexts](https://docs.browserbase.com/features/contexts) with Stagehand, run:

<Tabs>
	<Tab title="npm">
	```bash
	npx create-browser-app --example persist-context
	```
	</Tab>

	<Tab title="pnpm">
	```bash
	pnpm create browser-app --example persist-context
	```
	</Tab>

	<Tab title="yarn">
	```bash
	yarn create browser-app --example persist-context
	```
	</Tab>
</Tabs>

You can easily deploy Stagehand scripts to [Vercel](https://vercel.com/) in one line! Simply run:

<Tabs>
	<Tab title="npm">
	```bash
	npx create-browser-app --example deploy-vercel
	npx vercel deploy
	```
	</Tab>

	<Tab title="pnpm">
	```bash
	pnpm create browser-app --example deploy-vercel
	```
	</Tab>

	<Tab title="yarn">
	```bash
	yarn create browser-app --example deploy-vercel
	```
	</Tab>
</Tabs>

We also have an example using CUA agents, where we put OpenAI Operator against Anthropic's Claude Computer Use in a heated game of chess. It's just ten lines of Stagehand code, try it with:

<Tabs>
	<Tab title="npm">
	```bash
	npx create-browser-app --example chess
	```
	</Tab>

	<Tab title="pnpm">
	```bash
	pnpm create browser-app --example chess
	```
	</Tab>

	<Tab title="yarn">
	```bash
	yarn create browser-app --example chess
	```
	</Tab>
</Tabs>

You can also use the version of Stagehand that's on main but not released yet. This will have the bleeding edge features, but they may not be fully prod-ready yet. To test this version, try:

<Tabs>
	<Tab title="npm">
	```bash
	npx create-browser-app --alpha
	```
	</Tab>

	<Tab title="pnpm">
	```bash
	pnpm create browser-app --alpha
	```
	</Tab>

	<Tab title="yarn">
	```bash
	yarn create browser-app --alpha
	```
	</Tab>
</Tabs>
It will ask you the following questions:
```
✔ What is the name of your project? my-app
✔ Would you like to start with a quickstart example? Yes
✔ Select AI model: Anthropic Claude 3.7 Sonnet
✔ Run locally or on Browserbase? Local
✔ Run browser in headless mode (hide Chromium popup)?  No
```

</Step>

<Step title="Install dependencies and run the script">
<Tabs>
	<Tab title="npm">
	```bash
	cd my-app
	npm install
	npm run start
	```
	</Tab>

	<Tab title="pnpm">
	```bash
	cd my-app
	pnpm install
	pnpm run start
	```
	</Tab>

	<Tab title="yarn">
	```bash
	cd my-app
	yarn install
	yarn start
	```
	</Tab>
</Tabs>

Use the package manager of your choice to install the dependencies. We also have a `postinstall` script that will automatically install the Playwright browser with `playwright install`.
</Step>
</Steps>

### Adding Stagehand to an existing project: 

<Steps>

<Step title="Install Stagehand">

```bash
npm install @browserbasehq/stagehand
```

</Step>

<Step title="Set up environment variables">

Create a `.env` file or export environment variables:

```bash
export BROWSERBASE_API_KEY="your_browserbase_api_key"
export BROWSERBASE_PROJECT_ID="your_browserbase_project_id"
export OPENAI_API_KEY="your_openai_api_key"  # Configurable to other models
```

</Step>

<Step title="Create your first script">

Create a file `index.ts`:

```typescript
// index.ts
import { Stagehand, ConstructorParams } from "@browserbasehq/stagehand";
import dotenv from "dotenv";
import { z } from "zod";

dotenv.config();

export async function main() {
  const config: ConstructorParams = {
    env: "BROWSERBASE", // or "LOCAL"
    apiKey: process.env.BROWSERBASE_API_KEY,
    projectId: process.env.BROWSERBASE_PROJECT_ID,
    verbose: 1,
  };

  const stagehand = new Stagehand(config);

  try {
    await stagehand.init();
    const page = stagehand.page;

    await page.goto("https://docs.stagehand.dev/");
    await page.act("click the quickstart link");

    const result = await page.extract({
      instruction: "extract the main heading of the page",
      schema: z.object({
        heading: z.string(),
      }),
    });

    console.log(`Extracted: ${result.heading}`);
  } catch (error) {
    console.error(error);
  } finally {
    await stagehand.close();
  }
}

main();
```
</Step>

<Step title="Run the script">

```bash
npx tsx index.ts
```

</Step>
</Steps>

</Tab>

<Tab title="Python">
Before you begin, you'll need Python 3.9+ installed. We recommend using [uv](https://docs.astral.sh/uv/) to manage your virtual environment.

<Steps>
<Step title="Install Stagehand Python">

```bash
pip install stagehand
```

</Step>

<Step title="Set up environment variables">

Create a `.env` file or export environment variables:

```bash
export BROWSERBASE_API_KEY="your_browserbase_api_key"
export BROWSERBASE_PROJECT_ID="your_browserbase_project_id"
export MODEL_API_KEY="your_model_api_key"  # OpenAI, Anthropic, etc.
```

</Step>

<Step title="Create your first script">

Create a file `main.py`:

```python
import asyncio
import os
from stagehand import Stagehand, StagehandConfig
from dotenv import load_dotenv

load_dotenv()

async def main():
    config = StagehandConfig(
        env="BROWSERBASE",
        api_key=os.getenv("BROWSERBASE_API_KEY"),
        project_id=os.getenv("BROWSERBASE_PROJECT_ID"),
        model_name="gpt-4o",
        model_api_key=os.getenv("MODEL_API_KEY")
    )
    
    stagehand = Stagehand(config)
    
    try:
        await stagehand.init()
        page = stagehand.page
        
        await page.goto("https://docs.stagehand.dev/")
        await page.act("click the quickstart link")
        
        result = await page.extract("extract the main heading of the page")
        
        print(f"Extracted: {result}")
        
    finally:
        await stagehand.close()

if __name__ == "__main__":
    asyncio.run(main())
```
</Step>

<Step title="Run the script">

```bash
python main.py
```

</Step>
</Steps>

</Tab>
</Tabs>

**Next Steps**

<CardGroup cols={2}>
<Card
  title="Check out the TypeScript repo"
  icon="github"
  href="https://github.com/browserbase/stagehand"
>
  If you're a TypeScript developer, you can check out the Stagehand TypeScript repo. PRs welcome!
</Card>

<Card
  title="Check out the Python repo"
  icon="github"
  href="https://github.com/browserbase/stagehand-python"
>
  Stagehand now supports Python! The Python repo is brand new, so PRs are more than welcome!
</Card>
</CardGroup>