---
title: "Browserbase MCP Server Tools"
sidebarTitle: "Tools"
description: "This guide covers the specialized tools available in the Browserbase MCP server for browser automation and interaction."
---

## Overview

The Browserbase MCP server provides comprehensive tools for browser automation and session management. These tools allow you to perform actions like navigating pages, capturing screenshots, manipulating cookies, and managing multiple browser sessions simultaneously.

## Core Browser Automation Tools

These are the primary tools for modern web automation using natural language commands.

<Accordion title="browserbase_stagehand_navigate">
Navigate to any URL in the browser

<ParamField path="url" type="string" required>
  The URL to navigate to
</ParamField>
</Accordion>

<Accordion title="browserbase_stagehand_act">
Perform an action on the web page using natural language

<ParamField path="action" type="string" required>
  The action to perform (e.g., "click the login button", "fill form field")
</ParamField>

</Accordion>

<Accordion title="browserbase_stagehand_extract">
Extract all text content from the current page (filters out CSS and JavaScript)

<ParamField path="instruction" type="string">
  Extracted text content from the current page
</ParamField>
</Accordion>

<Accordion title="browserbase_stagehand_observe">
Observe and find actionable elements on the web page

<ParamField path="instruction" type="string" required>
  Specific instruction for observation (e.g., "find the login button", "locate search form")
</ParamField>

</Accordion>

<Accordion title="browserbase_screenshot">
Capture a PNG screenshot of the current page

<Info>No input parameters required</Info>

<ResponseField name="image" type="string">
  Base-64 encoded PNG data
</ResponseField>
</Accordion>

## Single Session Management

Traditional approach with one active browser session. Simpler for basic automation tasks and automatically manages the active session.

<Accordion title="browserbase_session_create">
Create or reuse a cloud browser session using Browserbase with fully initialized Stagehand

<ParamField path="sessionId" type="string">
  Optional session ID to use/reuse. If not provided, creates new session
</ParamField>

</Accordion>

<Accordion title="browserbase_session_close">
Close the current Browserbase session, disconnect the browser, and cleanup Stagehand instance

<Info>No input parameters required</Info>

</Accordion>

## Multi-Session Management

Advanced approach with multiple parallel browser sessions for complex automation workflows. Each session maintains independent state, cookies, and browser context.

### Session Lifecycle Management

<Accordion title="multi_browserbase_stagehand_session_create">
Create a new independent Stagehand browser session with full web automation capabilities

<ParamField path="name" type="string">
  Human-readable name for tracking (e.g., 'login-flow', 'data-scraping')
</ParamField>
</Accordion>

<Accordion title="multi_browserbase_stagehand_session_list">
List all currently active Stagehand browser sessions with detailed metadata

<Info>No input parameters required</Info>

</Accordion>

<Accordion title="multi_browserbase_stagehand_session_close">
Close and clean up a specific Stagehand browser session

<ParamField path="sessionId" type="string" required>
  Exact session ID to close (cannot be undone)
</ParamField>
</Accordion>

### Session-Specific Automation Tools

All core browser automation tools are available with session-specific variants:

<Accordion title="multi_browserbase_stagehand_navigate_session">
Navigate to a URL in a specific browser session

<ParamField path="sessionId" type="string" required>
  The session ID to use
</ParamField>

<ParamField path="url" type="string" required>
  The URL to navigate to
</ParamField>
</Accordion>

<Accordion title="multi_browserbase_stagehand_act_session">
Perform an action in a specific browser session using natural language

<ParamField path="sessionId" type="string" required>
  The session ID to use
</ParamField>

<ParamField path="action" type="string" required>
  The action to perform
</ParamField>

</Accordion>

<Accordion title="multi_browserbase_stagehand_extract_session">
Extract structured information from a specific browser session

<ParamField path="sessionId" type="string" required>
  The session ID to use
</ParamField>

<ParamField path="instruction" type="string" required>
  What to extract from the page
</ParamField>
</Accordion>

<Accordion title="multi_browserbase_stagehand_observe_session">
Observe and find actionable elements in a specific browser session

<ParamField path="sessionId" type="string" required>
  The session ID to use
</ParamField>

<ParamField path="instruction" type="string" required>
  What to observe (e.g., "find the login button")
</ParamField>

<ParamField path="returnAction" type="boolean">
  Whether to return the action to perform
</ParamField>
</Accordion>

### Multi-Session Use Cases

<CardGroup cols={2}>
  <Card title="Parallel Data Collection" icon="globe">
    Run multiple scraping sessions simultaneously across different websites
  </Card>
  
  <Card title="A/B Testing" icon="flask">
    Compare user flows across different browser sessions with varying configurations
  </Card>
  
  <Card title="Cross-Site Operations" icon="link">
    Perform coordinated actions across multiple websites or applications
  </Card>
  
  <Card title="Backup Sessions" icon="shield">
    Keep fallback sessions ready in case primary sessions encounter issues
  </Card>
</CardGroup>

## Resources

<Card title="Screenshot Resources" icon="camera">
  The server provides access to screenshot resources with URI-based access. 

  example: 
  ```
  screenshot://screenshot-name-of-the-screenshot
  ```

</Card>

## Further Reading

<CardGroup cols={3}>
<Card title="Model Context Protocol (MCP) Docs" icon="book" href="https://modelcontextprotocol.io/introduction">
Learn more about the MCP protocol
</Card>

<Card title="Stagehand Documentation" icon="robot" href="https://docs.stagehand.dev/">
Explore Stagehand's AI-powered browser automation
</Card>

<Card title="Support" icon="headset" href="mailto:<EMAIL>">
Get help from our support team
</Card>
</CardGroup>